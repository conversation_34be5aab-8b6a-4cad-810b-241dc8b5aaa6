

<template>
  <div class="home-page">
    <eleTopmenu />
    <div class="scroll-wrapper">
      <section class="screen">
        <homePage />
      </section>
      <section class="screen">
        <SplashText />
      </section>
      <section class="screen">
        <GsapVisual />
      </section>
      <section class="screen">
        <GsapCloud />
      </section>
      <section class="screen">
        <GsapBusiness />
      </section>
      <section class="screen">
        <GaspAi />
      </section>
      <brandSwiper />
      <FooterNav />
    </div>
  </div>
</template>

<script>
import GsapVisual from '@/components/gsapVisual.vue'
import GsapCloud from '@/components/gsapCloud.vue'
import GsapBusiness from '@/components/gsapBusiness.vue'
import GaspAi from '@/components/gsapAi.vue'
import brandSwiper from '@/components/mouthSwiper.vue'
import FooterNav from '@/components/bottom.vue'
import SplashText from '@/components/gsapOpenDisplay.vue'
import eleTopmenu from '@/components/eleTopmenu.vue'
import homePage from '@/components/homePage.vue'

export default {
  name: 'HomeView',
  components: {
    GsapVisual,
    GsapCloud,
    GsapBusiness,
    GaspAi,
    brandSwiper,
    FooterNav,
    SplashText,
    eleTopmenu,
    homePage
  }
}
</script>