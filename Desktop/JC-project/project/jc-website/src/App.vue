<template>
  <div id="app">
    <!-- 可选导航 -->
    <router-link to="/">首页</router-link>

    <!-- 路由内容将渲染在这里 -->
    <router-view />
  </div>
</template>

<script>
export default {
  name: 'App'
}
</script>

<style>
.scroll-wrapper {
  overflow-y: scroll;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none;  /* IE 10+ */
}
.scroll-wrapper::-webkit-scrollbar {
  display: none; /* Chrome / Safari */
}
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

#app {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.scroll-wrapper {
  height: 100vh;
  width: 100%;
  overflow-y: scroll;
}

.screen {
  min-height: 100vh;
}


.el-menu--popup {
  background-color: #fff !important;
  /* border-radius: 8px; */
}
.el-menu--popup .el-menu-item {
  background-color: #fff !important;
  color: #271373 !important;
  font-weight: bold;
  border-bottom: 1px solid #F3F3F3;
  font-size: 13px;
}
.el-menu--popup .el-menu-item:hover {
  background-color: #F3F3F3 !important; /* 你想要的颜色 */
  color: #3865F3 !important;
}

</style>
