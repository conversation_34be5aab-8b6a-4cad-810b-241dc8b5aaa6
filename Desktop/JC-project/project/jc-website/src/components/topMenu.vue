<template>
    <div class="top-menu">
        <div :style="titleStyle">Tec-do2.0 钛动科技</div>
        <div :style="menuStyle">
            <el-menu  :default-active="activeIndex" class="el-menu-demo" :mode="mode" background-color="#000" text-color="#fff" active-text-color="#fff" @select="handleSelect">
            <el-menu-item index="1" >钛动云</el-menu-item>
            <el-menu-item index="2" >钛动智能</el-menu-item>
            <el-submenu index="3" >
                <template slot="title" >出海解决方案</template>
                <el-menu-item index="3-1" >选项1</el-menu-item>
                <el-menu-item index="3-2">选项2</el-menu-item>
                <el-menu-item index="3-3">选项3</el-menu-item>
                <el-submenu index="3-4">
                <template slot="title">选项3</template>
                <el-menu-item index="3-4-1">选项1</el-menu-item>
                <el-menu-item index="3-4-2">选项2</el-menu-item>
                <el-menu-item index="3-4-3">选项3</el-menu-item>
                </el-submenu>
            </el-submenu>
            <el-submenu index="3" >
                <template slot="title" >案例专栏</template>
                <el-menu-item index="3-1" >选项1</el-menu-item>
                <el-menu-item index="3-2">选项2</el-menu-item>
                <el-menu-item index="3-3">选项3</el-menu-item>
                <el-submenu index="3-4">
                <template slot="title">选项3</template>
                <el-menu-item index="3-4-1">选项1</el-menu-item>
                <el-menu-item index="3-4-2">选项2</el-menu-item>
                <el-menu-item index="3-4-3">选项3</el-menu-item>
                </el-submenu>
            </el-submenu>
            <el-menu-item index="4" >电商解决方案</el-menu-item>
            <el-menu-item index="5" >钛动出海研究院</el-menu-item>
            <el-menu-item index="6" >企业动态</el-menu-item>
            <el-menu-item index="7" >关于我们</el-menu-item>

             <el-submenu index="8" >
                 <template slot="title" >简体中文</template>
                <el-menu-item index="8-1" >选项1</el-menu-item>
                <el-menu-item index="8-2">选项2</el-menu-item>
            </el-submenu>
            <!-- <el-menu-item index="4"><a href="https://www.ele.me" target="_blank">订单管理</a></el-menu-item> -->
            </el-menu>
        </div>

<div class="line"></div>

    </div>
</template>
<script>
    export default {
        name: 'TopMenu',
        data() {
            return {
                activeIndex: '1',
                activeIndex2: '1',
                mode: 'horizontal',
                windowWidth: window.innerWidth,
                windowHeight: window.innerHeight,
                titleStyle: {
                    color: '#fff',
                    textAlign: 'center',
                    lineHeight: '60px',
                    fontSize: '20px',
                    fontWeight: 'bold'
                },
                menuStyle: {
                    width: '30%',
                    border: '1px solid #fff',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center'

                }
            };
        },
        mounted() {
            // 监听窗口大小变化
            window.addEventListener('resize', this.handleResize);
        },
        beforeDestroy() {
            // 移除窗口大小变化监听
            window.removeEventListener('resize', this.handleResize);
        },
        methods: {
            // 处理窗口大小变化
            handleResize() {
                this.windowWidth = window.innerWidth;
                this.windowHeight = window.innerHeight;
                // if (this.windowWidth < 2016) {
                //     this.mode = 'vertical';
                // } else {
                //     this.mode = 'horizontal';
                // }
                console.log('窗口尺寸变化:', this.windowWidth, this.windowHeight);
            },
            // 处理菜单项选择事件
            handleSelect(key, keyPath) {
                console.log(key, keyPath);
            }
        }
    };
</script>
<style scoped >
.top-menu {
    border: 1px solid red;
    width: 100%;
    height: 30px;
    background-color:#a10909;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0, 21, 41, 0.2);
    display: flex;
    flex-direction: row;
    align-content: center;
    justify-content: center;
}
</style>