<template>
    <div style="display: flex;justify-content: center;align-items: center;padding: 100px 0 80px 0;">
        <div style="display: flex;flex-direction: column;justify-content: center;align-items: center;width: 78%;">
            <div ref="hiddenText" style="display: flex;flex-direction: row;justify-content: space-around;align-items: center;width: 100%; opacity: 0; transform: translateY(100px);">
                <div style="font-size: 30px;">
                    <h2>全球领先的“云+智能”解决方案</h2>
                    <h2>驱动企业全球化增长</h2>
                </div>
                <p style="color: #767676;font-size: 20px;line-height: 1.75rem;width: 840px;">
                    钛动科技通过AI智能、BI洞察与强大的产品矩阵，打造“云+智能”全链路解决方案，整合全球数字媒体资源，搭建优质交易通道，为出海全行业客户提供强大的运营技术保障和数字化工具支持，实现交易精确匹配和高效管理，成就出海高增长。
                </p>
            </div>
            <div ref="hiddenImg" style="opacity: 0; transform: translateY(100px);">
                <img style="width: 100%; height: 100%;" src="https://www.tec-do.com/static/home/<USER>/image/home/<USER>" alt="">
            </div>
        </div>

    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import gsap from 'gsap'

const hiddenText = ref(null)
const hiddenImg = ref(null)

onMounted(() => {
  const options = {
    threshold: 0.1
  }

  const textObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        gsap.fromTo(hiddenText.value,
          { opacity: 0, y: 100 },
          { opacity: 1, y: 0, duration: 1, ease: 'power2.out' }
        )
        observer.unobserve(entry.target)
      }
    })
  }, options)

  const imgObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        gsap.fromTo(hiddenImg.value,
          { opacity: 0, y: 100 },
          { opacity: 1, y: 0, duration: 1, ease: 'power2.out', delay: 0.3 }
        )
        observer.unobserve(entry.target)
      }
    })
  }, options)

  if (hiddenText.value) {
    textObserver.observe(hiddenText.value)
  }
  if (hiddenImg.value) {
    imgObserver.observe(hiddenImg.value)
  }
})
</script>