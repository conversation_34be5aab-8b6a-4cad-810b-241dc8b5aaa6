<template>
  <div class="footer-container">
    <div class="footer-top">
      <img class="footer-logo" src="https://www.tec-do.com/static/home/<USER>/v2/logo-white.svg" alt="">
      <div class="footer-nav-list">
        <div class="footer-nav-item nav-item" v-for="(item, index) in navList" :key="index" @click="handleNavClick(item.link)">
          <a class="footer-nav-link" :href="item.link" target="_blank">{{ item.name }}</a>
        </div>
      </div>
    </div>
    <div class="footer-bottom">
      <div class="footer-left">
          <div class="footer-qr-section">
            <img class="footer-qr-image" src="https://www.tec-do.com/static/home/<USER>/v2/wechat-code.png" alt="">
            <span class="footer-qr-text">钛动科技微信公众号</span>
          </div>

          <div class="footer-contact">
            <span>广州市天河区高唐路265号天河</span>
            <span> 时代E-Park二期8栋 钛动科技中心</span>
            <span><EMAIL></span>
            <span>020-29026390</span>
          </div>
      </div>

      <div class="footer-info-links">
        <span class="footer-copyright">Copyright © 广州钛动科技股份有限公司</span>
        <a class="footer-link" href="https://beian.miit.gov.cn/"> 粤ICP备18007337号</a>
        <a class="footer-link" href="https://beian.miit.gov.cn/#/Integrated/index">粤公网安备 44010602009234</a>
        <a class="footer-link" href="https://www.tec-do.com/static/home/<USER>">《钛动科技隐私政策》</a>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'FooterNav',
    data() {
      return {
        navList:[
          { name: '钛动云', link: 'https://www.tec-do.com/about' },
          { name: '钛动智能', link: 'https://www.tec-do.com/contact' },
          { name: '出海解决方法', link: 'https://www.tec-do.com/privacy' },
          { name: '案例专栏', link: 'https://www.tec-do.com/terms' },
          { name: '钛动出海研究院', link: 'https://www.tec-do.com/about' },
          { name: '企业动态', link: 'https://www.tec-do.com/contact' },
          { name: '关于我们', link: 'https://www.tec-do.com/privacy' },
        ]
        // Define any data properties if needed
      };
    },
    methods: {
      handleNavClick(link) {
        window.open(link, '_blank');
      }
    }
  }
</script>
<style scoped>
.footer-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #000422;
  color: #fff;
  text-decoration: none;
  padding: 50px 0;
}
.footer-top {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 70%;
  border-bottom: 1px solid #363448;
}
.footer-logo {
  width: 300px;
  height: 150px;
}
.footer-nav-list {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 60%;
}
.footer-nav-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
}
.footer-nav-link {
  color: #fff;
  text-decoration: none;
  font-size: 23px;
}
.footer-nav-link:hover {
  color: #4d7bfe;
  text-decoration: none;
  transition: all 0.3s ease;
}
.footer-bottom {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-end;
  width: 70%;
  margin-top: 50px;
}
.footer-left {
  display: flex;
  flex-direction: row;
}
.footer-qr-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #919192;
}
.footer-qr-image {
  width: 100px;
  height: 100px;
}
.footer-qr-text {
  line-height: 30px;
}
.footer-contact {
  color: #919192;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  margin-left: 50px;
  line-height: 30px;
}
.footer-info-links {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
  width: 48%;
}
.footer-link {
  color: #8B8B8D;
  text-decoration: none;
}
.footer-copyright {
  color: #8B8B8D;
}
</style>