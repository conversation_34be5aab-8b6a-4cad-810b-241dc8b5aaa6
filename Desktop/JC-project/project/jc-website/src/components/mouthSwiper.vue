<template>
    <!--  -->
      <div class="container">
        <img class="swipe-back" src="https://www.tec-do.com/static/home/<USER>/image/home/<USER>/customer-review-bg.png" alt="">
        <h1 style="font-size: 62px;">口碑见证：选择我们的理由</h1>
        <div class="swiper-container" ref="mySwiper">
            <div class="swiper-wrapper" style="height: 500px;">
                <div class="swiper-slide" v-for="(item, index) in slides" :key="index"
                 >
                    <!-- <img :src="item" class="slide-image" /> -->
                    <div class="slide-content-img">
                        <img  style="object-fit: cover;height: 100%;width: 100%;" :src="item.cartooAvatar" alt="">
                    </div>
                    <div class="slide-content">
                        <img style="height: 110px;width: 235px;"  :src="item.brandLogo" alt="">
                        <p class="synopsis">{{ item.synopsis }}</p>
                        <span class="identity" style="color: #0750fe;font-weight: bold;font-size: 25px;margin-top: 70px;">{{ item.identity }}</span>
                    </div>
                </div>
            </div>

            <!-- 如果需要分页器 -->
            <!-- <div class="swiper-pagination"></div> -->

            <!-- 如果需要导航按钮 -->
            <div class="swiper-button-prev"></div>
            <div class="swiper-button-next"></div>
        </div>
      </div>
</template>

<script>
import Swiper from 'swiper'
import 'swiper/css/swiper.css'

export default {
  name: 'brandSwiper',
  data() {
    return {
        slides: [
            {
                cartooAvatar:'https://www.tec-do.com/static/home/<USER>/image/home/<USER>/361-avatar.png',
                brandLogo:'https://www.tec-do.com/static/home/<USER>/image/home/<USER>/361-logo.png',
                synopsis:'"钛动科技是我们非常信赖的合作伙伴，在电商平台运营方面有成熟经验，有效帮助我们提升在东南亚市场的品牌效应，打造差异化亮点，进而拉动销量  进入东南亚初期，我们需要迅速提升货品宽度和丰富度，刷新品牌形象。钛动科技通过本土人群画像洞察、系统提升品类宽度、全生命周期代运营和站内玩法迭代等多重策略，帮助我们精准拓客。"',
                identity:'361°跨境商品运营负责人 Aling',
            },
             {
                cartooAvatar:'https://www.tec-do.com/static/home/<USER>/image/home/<USER>/sikaiqi-avatar.png',
                brandLogo:'https://www.tec-do.com/static/home/<USER>/image/home/<USER>/sikaiqi-logo.png',
                synopsis:'"钛动科技帮助我们深入东南亚市场，在“双十一”等大促活动中，通过TikTok Shop电商服务，帮我们收获了巨大的流量和广泛的种草、展现了丰富内容和优价好货，大幅提升GMV表现。"',
                identity:'东南亚地区电商负责人 Bernard Yap',
            },
             {
                cartooAvatar:'https://www.tec-do.com/static/home/<USER>/image/home/<USER>/chaoming-avatar.png',
                brandLogo:'https://www.tec-do.com/static/home/<USER>/image/home/<USER>/chaoming-logo.png',
                synopsis:'"钛动科技是我们非常信赖的合作伙伴，在游戏发行与达人合作方面专业，尤其是人群洞察与媒体选择方面表现非常成熟。在美国、日本、韩国等重点市场的发行策略，为我们提供了很好的帮助。"',
                identity:' 鸣潮 全球发行负责人 Ken',
            },
             {
                cartooAvatar:'https://www.tec-do.com/static/home/<USER>/image/home/<USER>/mofang-avatar.png',
                brandLogo:'https://www.tec-do.com/static/home/<USER>/image/home/<USER>/mofang-logo.png',
                synopsis:'"钛动科技的达人资源非常丰富，是值得信任的好伙伴。在开拓全球市场时，本土化服务能力很重要，钛动科技在这方面提供的支持很出色，客户支持团队也很给力，响应速度很快，并总能提供专业洞察，这种高水平的服务让我感到非常安心。"',
                identity:'腾讯魔方工作室海外市场营销经理 Tenn',
            },
             {
                cartooAvatar:'https://www.tec-do.com/static/home/<USER>/image/home/<USER>/tebu-avatar.png',
                brandLogo:'https://www.tec-do.com/static/home/<USER>/image/home/<USER>/tebu-logo.png',
                synopsis:'"在特步出海的过程中，一直和钛动保持密切合作，品牌营销效应显著，跑友们常常自发转发种草特步。在Shopee平台上，特步的销量还在快速上升。"',
                identity:'特步跨境运营负责人 Luo ',
            }
            // 'https://www.tec-do.com/uploads/images/2024-12-10-11-35-25.jpg',
            // 'https://www.tec-do.com/uploads/images/2025-05-29-17-54-17.jpg',
            // 'https://www.tec-do.com/uploads/images/2024-02-21-18-29-01.png',
            // 'https://www.tec-do.com/uploads/images/2023-11-01-11-10-06.jpg',
            // 'https://www.tec-do.com/uploads/images/2025-05-26-09-46-29.jpg',
        ]
    }
  },
  mounted() {
    this.mySwiper = new Swiper(this.$refs.mySwiper, {
        loop: true,
        centeredSlides: true, // ✅ 让中间的 slide 居中
        slidesPerView: 'auto', // ✅ 自动计算宽度
        spaceBetween: 30, // ✅ 每个 slide 之间的间距
        pagination: {
            el: '.swiper-pagination',
            clickable: false
        },
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev'
        },
        autoplay: {
            delay: 2000
        }
    })
  }
}
</script>

<style scoped>
.container{
    width: 100%;
    height: 80vh;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
}

.swiper-container {
    height: 600px;
    width: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
    padding-top: 50px;
}

/* .swiper-wrapper{
    display: flex;
    justify-content: center;
    align-content: center;
} */

.swiper-slide{
    height: 100%;
    width: 60%;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.057);
    backdrop-filter: blur(50px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 12px;
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
}

.slide-content {
    background-color: #fff;
    border-radius: 20px;
    width: 70%;
    height: 80%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    padding: 20px;
}

.synopsis{
    font-size: 23px;
    color: #4c4c4c;
    margin-top: 30px;
}

.slide-content-img{
    width: 400px;
    height: 100%;
}

.slide-image {
    width: 50%;
    height: auto;
    object-fit: cover;
}
.swipe-back{
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: -1;
    object-fit: cover;
}
</style>