<template>
    <div>
        <div class="top-menu" :style=" { height: topMenuHeight + 'px' }"  :class="windowWidth<960?'navAuto':''">
            <div class="tag" :style="titleStyle">聚创集团 | SiNOX</div>
            <div :style="menuStyle" class="menu-container" v-if="isShow">
                <div
                  class="menu-item"
                  v-for="(item, index) in menulist"
                  :key="index"
                  @mouseenter="handleMouseEnter(index)"
                  @mouseleave="handleMouseLeave(index)"
                >
                  <div class="fixedStyle" @click="handleSelect(index)">
                    {{ item.label }}
                    <img class="fixedImg" v-if="item.tag" :src="item.tag" alt="tag" :class="index == '4'? 'i4' : 'i5' " >
                  </div>

                 <el-collapse-transition >
                     <!--  -->
                    <div class="submenu" @mouseenter="handleSubmenuEnter(index)" @mouseleave="handleSubmenuLeave(index)"  v-if="(hoverIndex === index || submenuHoverIndex === index) && item.children">
                        <div class="submenu-info" >
                            <img class="submenu-img"  src="https://www.tec-do.com/static/home/<USER>/v2/header/case/nav-bg-2.jpg" alt="">
                            <div class="submenu-info-in" >
                                <span style="font-size: 35px;">出海解决方案</span>
                                <span class="info-in-text" >钛动科技贯通出海全流程服务体系，针对性制定电商、品牌、应用和游戏各类出海解决方案，助力中国品牌走向全球。</span>
                            </div>

                        </div>
                        <div class="submenu-list">
                            <div v-for="child in item.children" :key="child.index" class="submenu-item" >
                                <span style="font-size: 23px;">{{ child.label }}</span>
                                <span style="font-size: 17px;font-weight: 400;color: #606060">{{ child.detailed }}</span>
                                <img style="width: 700px;height: 30px;margin-top: 30px;" :src="child.img" alt="">
                            </div>
                        </div>

                    </div>
                  </el-collapse-transition>
                </div>
            </div>
            <div v-else style="display: flex; align-items: center; justify-content: space-around; flex-direction: row; color: #fff;width: 100px;">
                <p>EN</p>
                <p>三</p>
            </div>
        </div>
    </div>
</template>
<script>
    export default {
        name: 'CustomizeMenu',
        data() {
            return {
                menulist:[
                    {
                        index: '1',
                        label: 'Home',
                        children: [
                            { index: '3-1', label: '电商出海', detailed:'提供跨境电商一站式全球推广服务',img:'https://www.tec-do.com/static/home/<USER>/v2/header/case/1644.png'},
                            { index: '3-2', label: '品牌出海', detailed:'全案出海战略提升海外品牌竞争力',img:'https://www.tec-do.com/static/home/<USER>/v2/header/case/1646.png'},
                            { index: '3-3', label: '应用出海', detailed:'帮助应用精准触达海外高质量用户',img:'https://www.tec-do.com/static/home/<USER>/v2/header/case/1645.png'},
                            { index: '3-4', label: '游戏出海', detailed:'助力游戏发行商逐鹿海外',img:'https://www.tec-do.com/static/home/<USER>/v2/header/case/1643.png'},
                        ]
                    },
                    {
                        index: '2',
                        label: 'About Us'
                    },
                    {
                        index: '3',
                        label: 'Services',

                    },
                    {
                        index: '4',
                        label: 'Shop',
                        // tag:'https://www.tec-do.com/static/home/<USER>/nav_hot.png'
                    },
                    {
                        index: '5',
                        label: 'Pages',
                        // tag:'https://www.tec-do.com/static/home/<USER>/nav_badge_02_x2.png'
                    },
                    {
                        index: '6',
                        label: 'Blog'
                    },
                    {
                        index: '7',
                        label: 'Contact'
                    },
                    {
                        // index: '8',
                        // label: '简体中文',
                        // children:[
                        //     {index:'8-1',label:'选项1'},
                        //     {index:'8-2',label:'选项2'}
                        // ]
                    }
                ],
                windowWidth: window.innerWidth,
                windowHeight: window.innerHeight,
                topMenuHeight: 84,
                isShow: true,
                titleStyle: {
                    fontSize: '35px',
                },
                menuStyle: {
                    width: '65%',
                    fontSize: '23px',
                },
                hoverIndex: null,
                submenuHoverIndex: null,
            };
        },
        mounted() {
            // 监听窗口大小变化
            window.addEventListener('resize', this.handleResize);
        },
        beforeDestroy() {
            // 移除窗口大小变化监听
            window.removeEventListener('resize', this.handleResize);
        },
        methods: {
            // 处理窗口大小变化
            handleResize() {
                this.windowWidth = window.innerWidth;
                this.windowHeight = window.innerHeight;
                // console.log('窗口尺寸变化:', this.windowWidth, this.windowHeight);
                const baseWidth = 1920;
                const scale = Math.min(this.windowWidth / baseWidth, 1);
                // 等比例缩放菜单字体大小
                const fontSize = 23 * scale;
                this.menuStyle.fontSize = `${fontSize}px`;
                // 等比例缩放标题字体大小，最小为 30px
                const baseTitleFontSize = 35; // 当前初始大小
                const titleFontSize = Math.max(baseTitleFontSize * scale, 25);
                // 统一设置 titleStyle.fontSize 为 titleFontSize，无论窗口宽度
                if (this.windowWidth < 960) {
                    this.isShow = false; // 隐藏菜单
                    this.titleStyle = {
                        ...this.titleStyle,
                        fontSize: `${titleFontSize}px`,
                        marginRight: '50px',
                    };
                } else {
                    this.isShow = true; // 显示菜单
                    this.titleStyle = {

                        fontSize: `${titleFontSize}px`,
                    };
                }
                // this.topMenuHeight = 100 * scale; //菜单高度跟随窗口缩放
            },
            // 处理菜单项选择事件
            handleSelect(i) {
                console.log('选中菜单项:', this.menulist[i].index, this.menulist[i].label);
                // console.log(key, keyPath);
            },
            // 主菜单鼠标进入事件
            handleMouseEnter(index) {
                this.hoverIndex = index;
            },
            handleMouseLeave(index) {
                setTimeout(() => {
                if (this.hoverIndex === index && this.submenuHoverIndex !== index) {
                    this.hoverIndex = null;
                }
                }, 100);
            },
            // 子菜单鼠标进入事件
            handleSubmenuEnter(index) {
                this.submenuHoverIndex = index;
            },
            handleSubmenuLeave(index) {
                setTimeout(() => {
                  if (this.submenuHoverIndex === index) {
                    this.submenuHoverIndex = null;
                  }
                  if (this.hoverIndex === index) {
                    this.hoverIndex = null;
                  }
                }, 100);
            }
        }
    };
</script>
<style scoped >
    .top-menu {
        width: 100vw;
        /* height: 20px; */
         /* 毛玻璃效果 */
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        background-color: rgba(0, 0, 0, 0.4);
        position: fixed;
        top: 0;
        left: 0;
        z-index: 1000;
        box-shadow: 0 2px 8px rgba(0, 21, 41, 0.2);
        display: flex;
        flex-direction: row;
        align-content: center;
        justify-content: flex-end;
    }
    .tag{
        display: flex;
        align-items: center;
        font-size: 30px;
        color: #fff;
        font-weight: bold;
        margin-right: 300px;
        background: linear-gradient(to right, #8D45FF, #3E3CFF, #4374FF);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        /* 可选：动画平滑过渡 */
        transition: background 0.3s ease;
    }
    .navAuto{
        display: flex;
        flex-direction: row;
        align-content: center;
        justify-content: center;
        height: 100px;
        /* width: 100%; */
    }
    .menu-container{
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-around;
        color: #fff;
        font-weight: bold;

    }
    .fixedStyle{
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        position: relative;
    }
    .fixedImg{
        margin-left: 10px;

    }
    .i4{
        width: 63px;
        height: 58px;
        margin-left: 10px;
        position: absolute;
        right: -48px;
        top: -28px  ;
    }
    .i5{
        width: 48px;
        height: 30px;
        margin-left: 10px;
        position: absolute;
        right: -50px;
        top: -13px  ;

    }

    /* 鼠标移入下拉窗口样式 */
    .submenu {
        position: fixed;
        top: 100px;
        left: 50%;
        transform: translateX(-50%);
        background: #fff;
        min-height: 550px;
        width: 100%;
        max-width: 100vw;
        z-index: 1001;
        display: flex;
        justify-content:space-between;
        flex-direction: row;
    }

    .submenu-info{
        width: 700px;
        height: 550px;
        position: relative;
        overflow: hidden;
        display: flex;
        align-content: flex-start;
        justify-content: center;
    }

    .submenu-img{
        width: 100%;
        height: 100%;
        object-fit: cover;
        position: absolute;
        z-index: -1;
    }

    .submenu-info-in{
       display: flex;
       flex-direction: column;
       align-items: flex-start;
       justify-content: center;
       width: 68%;
       height: 50%;
       color: #000;
    }

    .info-in-text{
        font-size: 18px;
        font-weight: 350;
        color: #606060
        ;margin-top: 50px;
    }
    .submenu-list{
        width: 100%;
        max-width: 100%;
        overflow: auto;
        display: flex;
        flex-direction: row;
        justify-content:space-between;
        align-items: center;
        flex-wrap: wrap;
        margin-left: 30px;
        padding:  0 15px;
    }
    .submenu-item {
        width: 800px;
        padding: 15px 20px;
        color: #fff;
        cursor: pointer;
        white-space: nowrap;
        display: flex;
        flex-direction: column;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        color: #000;
        margin-right: 100px;
        border-radius: 5px;
    }
    /* .active{
        margin-left: 20px;
    } */
    .submenu-item:hover {
        transition: 0.2s all linear;
        background-color: #fff;
        box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.15);

    }


    /* Highlight menu item text on hover */
   /* 主菜单文字颜色过渡 */
.fixedStyle {
  transition: color 0.3s ease;
}

/* 鼠标移入改变颜色 */
.menu-item:hover .fixedStyle {
  color: #25f4ee;
}


</style>
