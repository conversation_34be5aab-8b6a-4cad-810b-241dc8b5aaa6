<template>
  <div class="splash-screen" ref="splashScreen">
    <h1 ref="animatedText" class="splash-title">聚创集团｜SINOX</h1>

  </div>
</template>
<script>
import { gsap } from 'gsap';

export default {
  name: 'SplashText',
  mounted() {
    const titleEl = this.$refs.animatedText;
    const splashEl = this.$refs.splashScreen;

    const chars = titleEl.innerText.split('');
    titleEl.innerHTML = chars.map(char => `<span class="char">${char}</span>`).join('');

    // 缩放旋转入场动画，营造立体动感
    gsap.fromTo(titleEl,
      {
        opacity: 0,
        scale: 0.5,
        rotationX: 90,
        transformOrigin: 'center center'
      },
      {
        opacity: 1,
        scale: 1,
        rotationX: 0,
        duration: 2,
        ease: 'back.out(1.7)',
        delay: 0.3,
        onComplete: () => {
          const charEls = titleEl.querySelectorAll('.char');

          gsap.to(charEls, {
            opacity: 0,
            x: () => gsap.utils.random(-200, 200),
            y: () => gsap.utils.random(-150, 150),
            scale: () => gsap.utils.random(0.5, 1.5),
            rotate: () => gsap.utils.random(-90, 90),
            duration: 1,
            ease: 'power3.inOut',
            stagger: {
              amount: 0.5,
              from: 'center'
            }
          });

          gsap.to(splashEl, {
            backgroundColor: 'rgba(0, 0, 0, 0)',
            duration: 0.8,
            ease: 'power2.inOut',
            onComplete: () => {
              splashEl.style.pointerEvents = 'none';
              splashEl.style.opacity = '0.1';
              setTimeout(() => {
                splashEl.style.display = 'none';
              }, 300);
            }
          });
        }
      }
    );
  }
};
</script>
<style scoped>
.splash-screen {
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8); /* 纯黑背景 */
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999; /* 覆盖全屏 */
  transform-origin: center center; /* 设置缩放基准点为中心 */
  transition: opacity 0.6s ease;
}

.splash-title {
  font-size: 48px;
  font-weight: bold;
  color: transparent;
  background: linear-gradient(to right, #8D45FF, #3E3CFF, #4374FF);
  -webkit-background-clip: text;
  white-space: nowrap;
  transform-origin: top left;
}

.char {
  display: inline-block;
  opacity: 0;
}
</style>