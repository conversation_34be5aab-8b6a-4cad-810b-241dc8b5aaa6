<template>
    <div class="container">
        <img class="responsive-img" src="https://www.tec-do.com/static/home/<USER>/image/home/<USER>" alt="" >
        <div class="content-wrapper">
            <div class="border-box" ref="hiddenText" style="opacity: 0; transform: translateY(150px);">
                <span class="title-main">商业智能技术</span>
            </div>
            <div class="border-box" ref="hiddenModel" style="margin-top: 100px;opacity: 0; transform: translateY(150px);">
                <div class="border-box" >
                    <span class="title-sub">商业智能覆盖出海运营各关键环节，科技提效全球商业增长</span>
                    <div class="capacity-wrapper">
                        <div
                            v-for="(item, index) in capacityList"
                            :key="item.id"
                            :class="['capacity-item', { 'with-border': index === 1 }]"
                        >
                            <span class="title">{{ item.title }}</span>
                            <span class="description">{{ item.description }} </span>
                        </div>
                    </div>
                </div>
                <div class="border-box" style="margin-top: 50px;">
                    <span class="title-sub">盘活海量数据资产，全面提高企业数字化出海能力</span>
                    <div class="capacity-grid">
                        <template v-for="(item, index) in abroadList">
                            <div class="capacity-item-wrapper" :key="item.id">
                                <div class="capacity-item">
                                <span class="title">{{ item.title }}</span>
                                <span class="description">{{ item.description }}</span>
                                </div>
                                <div v-if="index % 2 === 0" class="vertical-divider"></div>
                            </div>
                            <div v-if="index === 1" :key="index" class="horizontal-divider"></div>
                        </template>
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>
<script>
    import gsap from 'gsap'
    export default {
        name: 'GsapBusiness',
        data() {
            return {
                capacityList: [
                    { title: '一站式多媒体数字资产管理', description: '10倍+提升账户管理效率' , id:1},
                    { title: '精细化智能投放运营', description: '10倍+提升投放效率',id:2 },
                    { title: '全天候自动监测和洞察', description: '秒级高效归因并优化决策',id:3 },
                ],
                abroadList: [
                    { title: '庞大数据底座', description: '日新增数据量8.2T', id:1 },
                    { title: '高并发数据处理能力', description: '广告点击性能响应低至3ms', id:2 },
                    { title: '领先的商业标签分析体系', description: '50+维度标签处理', id:3 },
                    { title: '知识产权数量行业领先', description: '近200项', id:4 },
                ]
            };
        },
        mounted() {
            const observer = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        if (entry.target === this.$refs.hiddenText) {
                            gsap.fromTo(this.$refs.hiddenText,
                                { opacity: 0, y: 100 },
                                { opacity: 1, y: 0, duration: 0.8, ease: 'power3.out' }
                            );
                        } else if (entry.target === this.$refs.hiddenModel) {
                            gsap.fromTo(this.$refs.hiddenModel,
                                { opacity: 0, y: 100 },
                                { opacity: 1, y: 0, duration: 0.8, ease: 'power3.out', delay: 0.2 }
                            );
                        }
                        observer.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.1 });

            if (this.$refs.hiddenText) observer.observe(this.$refs.hiddenText);
            if (this.$refs.hiddenModel) observer.observe(this.$refs.hiddenModel);
        }
    }

</script>
<style scoped>
.responsive-img {
  width: 100vw;
  height: 100vh;
  object-fit: cover;
  position: absolute;
  z-index: -1;
}

.container {
  position: relative;
  display: flex;
  justify-content: center;
  height: 100vh;
}

.content-wrapper {
  z-index: 1;
  color: #fff;
  width: 1000px;
  position: absolute;
  left: 45%;
  top: 20%;
  height: 70%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.border-box {

}

.title-main {
  font-size: 50px;
  font-weight: bold;
}

.title-sub {
  font-size: 28px;
  font-weight: bold;
}

.capacity-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  height: 150px;
  margin-top: 20px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
}

.capacity-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px;
}

.capacity-item .title {
  color: #25f4ee;
  font-size: 23px;
  font-weight: bold;
}

.capacity-item .description {
  font-size: 18px;
}

.capacity-item.with-border {
  border-left: 1px solid rgba(255, 255, 255, 0.2);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  width: 30%;
}

.capacity-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px 40px;
  padding: 20px 0;
  margin-top: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
  position: relative;
}
.capacity-item-wrapper {
  position: relative;
}

.vertical-divider {
  position: absolute;
  top: 10%;
  bottom: 10%;
  right: -20px;
  width: 1px;
  background-color: rgba(255, 255, 255, 0.3);
}

.horizontal-divider {
  grid-column: 1 / span 2;
  height: 1px;
  margin: 0 40px;
  background-color: rgba(255, 255, 255, 0.3);
}
</style>