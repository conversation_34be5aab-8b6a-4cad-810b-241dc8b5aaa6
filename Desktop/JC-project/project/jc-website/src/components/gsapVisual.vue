<template>
  <div class="container">
    <!-- 被滚动触发的图片和文字 -->
     <img class="container-img" src="https://www.tec-do.com/static/home/<USER>/image/home/<USER>" alt="">

    <div ref="box" class="animated-box">
      <!-- <img src="https://www.tec-do.com/uploads/images/2024-12-10-11-35-25.jpg" alt="示例图" /> -->
      <div style="font-size: 65px;display: flex;flex-direction: column;justify-content: center;align-items: flex-start;">
          <span>企业全球增长</span>
          <span>数字化服务商</span>
      </div>
      <div style="font-size: 30px;text-align: start;">
        <span >钛动科技凭借自主研发的全球数字媒体SaaS管理工具、领先的商业智能技术和人工智能技术，形成一套“云+智能”的全链路解决方案，为中国企业出海提供一站式数字化增长运营工具和服务，助力其成功拓展海外市场，实现全球化增长。</span>
      </div>
      <div style="text-align: start;cursor: pointer;">
        <h2>了解更多></h2>
      </div>
    </div>
  </div>
</template>

<script>
import gsap from 'gsap'

export default {
  name: 'GsapVisual',
  mounted() {
    const observer = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          gsap.fromTo(this.$refs.box,
            { opacity: 0, y: 350 },
            { opacity: 1, y: 0, duration:0.5, ease: 'power2.out' }  // 更快动画

          );
          observer.unobserve(entry.target);
        }
      });
    }, { threshold: 0.1 });

    observer.observe(this.$refs.box);
  },
}
</script>

<style scoped>
.container {
  height: 100vh; /* 模拟滚动 */
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ccc;
  position: relative;
}

.container-img{
  width: 100vw;
  height: 100vh;
  object-fit: cover;
  z-index: -1; /* 确保背景图片在最底层 */
}

.animated-box {
  width: 670px;
  height: 650px;
  position: absolute;
  left: 11.5%;
  text-align: center;
  padding: 70px;
  background: linear-gradient(180deg, #000bfe 0%, #0750fe 100%);
  color: #fff;
  /* border-radius: 12px; */
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  transition: all 0.3s linear;
  border: 1px solid #ccc;
  z-index: 1;
  opacity: 0;
  transform: translateY(350px);
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  align-content: start ;
}

</style>