<template>
    <div class="container">
        <img class="responsive-img" src="https://www.tec-do.com/static/home/<USER>/image/home/<USER>" alt="" >
        <div class="content-wrapper">
            <div class="border-box" ref="hiddenText" style="opacity: 0; transform: translateY(150px);">
                <span class="title-main">人工智能技术</span>
            </div>
            <div class="border-box" ref="hiddenModel" style="margin-top: 100px;opacity: 0; transform: translateY(150px);">
                <div class="border-box" >
                    <span class="title-sub">人工智能技术打通能力层、 模型层和应用层，突破效能上限</span>
                    <div class="capacity-wrapper">
                        <div
                            v-for="(item, index) in capacityList"
                            :key="item.id"
                            :class="['capacity-item', { 'with-border': index === 1 }]"
                        >
                            <img class="border-img" :src="item.img" alt="">
                            <span class="description">{{ item.description }} </span>
                        </div>
                    </div>
                </div>
                <div class="border-box" style="margin-top: 80px;">
                    <span class="title-sub">自研领域大模型， 闭环数字化运营增长链路</span>
                    <div class="capacity-wrapper">
                        <div
                            v-for="(item, index) in abroadList"
                            :key="item.id"
                            :class="['capacity-item', { 'with-border': index === 1 }]"
                        >
                            <img class="border-img" :src="item.img" alt="">
                            <span class="description">{{ item.description }} </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>
<script>
    import gsap from 'gsap'
    export default {
        name: 'GsapBusiness',
        data() {
            return {
                capacityList: [
                    { img: 'https://www.tec-do.com/static/home/<USER>/image/home/<USER>', description: '全域赋能内容运营链路' , id:1},
                    { img: 'https://www.tec-do.com/static/home/<USER>/image/home/<USER>', description: '自动化数据分析助力 创意智能迭代',id:2 },
                    { img: 'https://www.tec-do.com/static/home/<USER>/image/home/<USER>', description: '基于数据自动输出深度洞察',id:3 },
                ],
                abroadList: [
                    { img: 'https://www.tec-do.com/static/home/<USER>/image/home/<USER>', description: '70% 研发和交付团队', id:1 },
                    { img: 'https://www.tec-do.com/static/home/<USER>/image/home/<USER>', description: '产学研AI联合实验室', id:2 },
                    { img: 'https://www.tec-do.com/static/home/<USER>/image/home/<USER>', description: '90%+ 缩短内容生产时间', id:3 },
                ]
            };
        },
        mounted() {
            const observer = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        if (entry.target === this.$refs.hiddenText) {
                            gsap.fromTo(this.$refs.hiddenText,
                                { opacity: 0, y: 100 },
                                { opacity: 1, y: 0, duration: 0.8, ease: 'power3.out' }
                            );
                        } else if (entry.target === this.$refs.hiddenModel) {
                            gsap.fromTo(this.$refs.hiddenModel,
                                { opacity: 0, y: 100 },
                                { opacity: 1, y: 0, duration: 0.8, ease: 'power3.out', delay: 0.2 }
                            );
                        }
                        observer.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.1 });

            if (this.$refs.hiddenText) observer.observe(this.$refs.hiddenText);
            if (this.$refs.hiddenModel) observer.observe(this.$refs.hiddenModel);
        }
    }

</script>
<style  scoped>
.responsive-img {
  width: 100vw;
  height: 100vh;
  object-fit: cover;
  position: absolute;
  z-index: -1;
}

.container{
  position: relative;
  display: flex;
  justify-content: center;
  height: 100vh;
}

.content-wrapper {
  z-index: 1;
  color: #fff;
  width: 1000px;
  position: absolute;
  left: 15%;
  top: 20%;
  height: 70%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.border-img {
    width: 60px;
    height: 60px;
}

.title-main {
  font-size: 50px;
  font-weight: bold;
}

.title-sub {
  font-size: 28px;
  font-weight: bold;
}

.capacity-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 200px;
  margin-top: 20px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
}

.capacity-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px;

}

.capacity-item .title {
  color: #25f4ee;
  font-size: 23px;
  font-weight: bold;
}

.capacity-item .description {
  font-size: 18px;
}

.capacity-item.with-border {
  border-left: 1px solid rgba(255, 255, 255, 0.2);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  width: 20%;
  padding: 0 85px;
  margin: 0 50px;
  text-align: center;
}

.capacity-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px 40px;
  padding: 20px 0;
  margin-top: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
  position: relative;
}
.capacity-item-wrapper {
  position: relative;
}

.vertical-divider {
  position: absolute;
  top: 10%;
  bottom: 10%;
  right: -20px;
  width: 1px;
  background-color: rgba(255, 255, 255, 0.3);
}

.horizontal-divider {
  grid-column: 1 / span 2;
  height: 1px;
  margin: 0 40px;
  background-color: rgba(255, 255, 255, 0.3);
}
</style>