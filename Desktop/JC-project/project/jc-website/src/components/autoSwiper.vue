<template>
  <div class="case-wrapper">
    <!-- 顶部品牌 logo 横向滚动条 -->
    <div class="logo-swiper-container" style="border: 1px solid;" ref="logoSwiper">
      <div class="swiper-wrapper" style="border: 1px solid red;">
        <div
          v-for="(client, index) in clients"
          :key="index"
          class="logo-swiper-slide logo-slide" style="border: 1px solid blue;"
          :class="{ active: activeIndex === index }"
          :data-swiper-slide-index="index"
        >
          <img :src="client.logo" />
        </div>
      </div>
    </div>

    <!-- 主轮播内容 -->
    <div class="main-swiper-container" ref="mainSwiper"  style="overflow-x: hidden;">
      <div class="swiper-wrapper" >
        <div
          class="swiper-slide"
          v-for="(client, index) in clients"
          :key="index"
        >
          <div class="slide-image-left">
            <img :src="client.image" />
          </div>
          <div class="slide-info-right">
            <img :src="client.logo" class="brand-logo" />
            <p class="synopsis">{{ client.desc }}</p>
            <div class="metrics">
              <div v-for="(m, i) in client.metrics" :key="i" class="metric-item">
                <h3>{{ m.value }}</h3>
                <p>{{ m.label }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="swiper-button-prev"></div>
      <div class="swiper-button-next"></div>
      <div class="swiper-scrollbar"></div>
    </div>
  </div>
</template>

<script>
import Swiper from 'swiper'
import 'swiper/css/swiper.css'

export default {
  name: 'autoSwiper',
  data() {
    return {
      clients: [
        {
          logo: 'https://www.tec-do.com/static/home/<USER>/mobileNew/huohu.png',
          image: 'https://www.tec-do.com/static/home/<USER>/image/home/<USER>',
          desc: '钛动团队通过优化素材创意，如画面多维度更新、素材模板丰富化等手段，帮助MINISO在东南亚项目中成本与效果效率全面提升。',
          metrics: [
            { value: '100%', label: '连续6个月流量环比增长' },
            { value: '38%-44%', label: '次日留存率' },
            { value: '1000+', label: '月产出素材' }
          ]
        },
        {
          logo: 'https://www.tec-do.com/static/home/<USER>/mobileNew/TV.png',
          image: 'https://www.tec-do.com/static/home/<USER>/image/home/<USER>',
          desc: '钛动科技帮助TCL深入东南亚市场，通过精细化运营和本地化策略，实现品牌影响力和销售业绩的双重提升。',
          metrics: [
            { value: '200%', label: '销售额增长' },
            { value: '85%', label: '用户满意度' },
            { value: '5国', label: '重点市场覆盖' }
          ]
        },
        {
          logo: 'https://www.tec-do.com/static/home/<USER>/mobileNew/Bilibili_Logo.png',
          image: 'https://www.tec-do.com/static/home/<USER>/image/home/<USER>',
          desc: '钛动团队通过优化素材创意，如画面多维度更新、素材模板丰富化等手段，帮助Bilibili在东南亚项目中成本与效果效率全面提升。',
          metrics: [
            { value: '100%', label: '连续6个月流量环比增长' },
            { value: '38%-44%', label: '次日留存率' },
            { value: '1000+', label: '月产出素材' }
          ]
        },
        {
          logo: 'https://www.tec-do.com/static/home/<USER>/mobileNew/42.png',
          image: 'https://www.tec-do.com/static/home/<USER>/image/home/<USER>',
          desc: '钛动科技助力芒果TV出海，通过精准的市场定位和创新的营销方案，成功吸引大量海外用户。',
          metrics: [
            { value: '500万+', label: '海外下载量' },
            { value: '92%', label: '用户好评率' },
            { value: '30+', label: '覆盖国家/地区' }
          ]
        }
      ],
      activeIndex: 0,
      mainSwiper: null,
      logoSwiper: null
    }
  },
  mounted() {
    // 初始化 logo swiper
    this.logoSwiper = new Swiper(this.$refs.logoSwiper, {
      slidesPerView: 5,
      spaceBetween: 20,
      watchSlidesVisibility: true,
      watchSlidesProgress: true
    })

    // 初始化主轮播并与 logo swiper 联动
    this.mainSwiper = new Swiper(this.$refs.mainSwiper, {
      loop: true,
      loopedSlides: this.clients.length,
      slidesPerView: 'auto',
      centeredSlides: true,
      spaceBetween: 40,
      navigation: {
        nextEl: '.swiper-button-next',
        prevEl: '.swiper-button-prev'
      },
      autoplay: {
        delay: 3000,
        disableOnInteraction: false
      },
      scrollbar: {
        el: '.swiper-scrollbar',
        draggable: true
      },
      on: {
        slideChange: () => {
          const realIndex = this.mainSwiper?.realIndex ?? 0;
          this.activeIndex = realIndex;
        }
      }
    })
  }
}
</script>

<style scoped>
.case-wrapper {
  width: 100%;
  padding: 40px;
  background: #f6f6f6;
  box-sizing: border-box;
}

.logo-swiper-container {
  width: auto;
  max-width: 100%;
  margin: 0 auto 30px;
  overflow: hidden;
  display: flex;
    justify-content: center;
    align-items: center;
}

.logo-slide {
  width: 100px !important;
  height: 60px;
  opacity: 0.5;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
}
.logo-slide.active {
  opacity: 1;
  border-color: #0750fe;
  transform: scale(1.2);
  margin: 0 15px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
.logo-slide img {
  max-height: 40px;
  max-width: 90px;
  object-fit: contain;
}

.main-swiper-container {
  width: 100%;
  background: #fff;
  border-radius: 16px;
  padding: 30px;
  position: relative;
  overflow: visible;
  box-sizing: border-box;
  overflow-x: hidden;
}

.logo-swiper-slide {
  display: flex;
  justify-content: space-between;
  background: #f7f9fc;
  width: 160px !important;
  opacity: 0.5;
  border-radius: 12px;
  padding: 20px 40px;
  box-sizing: border-box;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.swiper-slide {
  display: flex;
  justify-content: space-between;
  background: #f7f9fc;
  width: 960px !important;
  opacity: 0.5;
  border-radius: 12px;
  padding: 20px 40px;
  box-sizing: border-box;
  transition: transform 0.3s ease, opacity 0.3s ease;
}
.swiper-slide.swiper-slide-active {
  opacity: 1;
  transform: scale(1.05);
}

.slide-image-left {
  flex: 0 0 300px;
  display: flex;
  align-items: center;
}
.slide-image-left img {
  width: 100%;
  height: auto;
  border-radius: 12px;
  object-fit: cover;
}

.slide-info-right {
  flex: 1;
  padding-left: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.brand-logo {
  height: 50px;
  margin-bottom: 20px;
  object-fit: contain;
}

.synopsis {
  font-size: 18px;
  color: #666;
  margin-bottom: 20px;
  line-height: 1.6;
  max-width: 500px;
}

.metrics {
  display: flex;
  justify-content: space-around;
  gap: 20px;
  margin-top: 30px;
}

.metric-item {
  text-align: center;
}

.metrics h3 {
  color: #0750fe;
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 6px;
}

.metrics p {
  font-size: 14px;
  color: #666;
  margin: 0;
}
.swiper-wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
  transition-timing-function: ease-in-out;
}

.swiper-scrollbar {
  height: 6px !important;
  border-radius: 0 !important;
  background: rgba(0, 0, 0, 0.1);
  margin-top: 20px;
}

.swiper-scrollbar-drag {
  background: #0750fe !important;
  border-radius: 0 !important;
}
</style>
