<template>
  <div :class="{ contend: true, scrolled: isScrolled }" >
      <!-- <div class="tag" >聚创集团 | SiNOX</div> -->
      <img src="../assets/img/logo.png" @click="goHome" :class="{logoFixed :true,logoCcroll:isLogo}"  alt="">
      <el-menu :default-active="activeIndex"  active-text-color="#000" text-color="#fff" background-color="rgba(255, 255, 255, 0)" class="el-menu-demo customStyle"  mode="horizontal" @select="handleSelect">
        <el-menu-item index="1" >Home</el-menu-item>
        <el-submenu index="2" >
          <template slot="title" >About Us</template>
          <el-menu-item index="2-1" >选项1</el-menu-item>
          <el-menu-item index="2-2">选项2</el-menu-item>
          <el-menu-item index="2-3">选项3</el-menu-item>
          <!-- <el-submenu index="2-4">
            <template slot="title">选项4</template>
            <el-menu-item index="2-4-1">选项1</el-menu-item>
            <el-menu-item index="2-4-2">选项2</el-menu-item>
            <el-menu-item index="2-4-3">选项3</el-menu-item>
          </el-submenu> -->
        </el-submenu>
        <el-menu-item index="3" >Services</el-menu-item>
        <el-menu-item index="4"><a href="https://www.ele.me" target="_blank">Shop</a></el-menu-item>
        <el-submenu index="5">
          <template slot="title">Pages</template>
          <el-menu-item index="5-1">选项1</el-menu-item>
          <el-menu-item index="5-2">选项2</el-menu-item>
          <el-menu-item index="5-3">选项3</el-menu-item>
        </el-submenu>
        <el-submenu index="6">
          <template slot="title">Blog</template>
          <el-menu-item index="6-1">选项1</el-menu-item>
          <el-menu-item index="6-2">选项2</el-menu-item>
          <el-menu-item index="6-3">选项3</el-menu-item>
        </el-submenu>
        <el-menu-item index="7" >contact</el-menu-item>
      </el-menu>
      <div style="width: 90px;height: 40px;background-color: #2154F2;border-radius: 5px;color: #fff;display: flex;justify-content: center;align-items: center;">Sign Up</div>
  </div>
</template>
<script>
  export default {
    name:'eleTopmenu',
    data() {
      return {
        activeIndex: '1',
        activeIndex2: '1',
        isScrolled: false,
        isLogo:false,
      };
    },
    mounted() {
      this.$nextTick(() => {
        // 检查父组件滚动
        const wrapper = document.querySelector('.scroll-wrapper');
        if (wrapper) {
          wrapper.addEventListener('scroll', this.handleScroll);
        }
      });
    },
    beforeDestroy() {
      const wrapper = document.querySelector('.scroll-wrapper');
      if (wrapper) {
        wrapper.removeEventListener('scroll', this.handleScroll);
      }
    },
    methods: {
      handleScroll(e) {
        const scrollTop = e.target.scrollTop;
        console.log('当前 scroll-wrapper 滚动高度：', scrollTop);
        this.isScrolled = scrollTop > 100;
        this.isLogo = scrollTop > 100;
      },
      handleSelect(key, keyPath) {
        console.log(key, keyPath);
      },
      goHome() {
        if (this.$route.path === '/') {
          this.$router.go(0); // 强制刷新当前页
        } else {
          this.$router.push('/');
        }
      }
    }
  }
</script>
<style  scoped>
    .contend{
        width: 100vw;
        position: fixed;
        top: 0;
        left: 0;
        z-index: 1000;
        display: flex;
        flex-direction: row;
        justify-content: space-around;
        align-items: center;
        background-color: #15104e !important;
        /* background-color: rgba(255, 255, 255, 0.2); */
        /* backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        transition: background-color 0.3s ease; */

    }
    .logoFixed{
      width: 260px;
      height: 68px;
      display: inline-block;
      cursor: pointer;
    }
    /* .logoCcroll {
      background: radial-gradient(circle, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 120%);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      transition: background 1s ease;
    } */
    .scrolled {
      background-color: #15104e !important;
      transition: background-color 1s ease;
    }
    .customStyle{
      border-bottom: none;
      font-weight: bold;
      width: auto;
      position: relative;
    }
   .tag{
      display: flex;
      align-items: center;
      font-size: 30px;
      color: #fff;
      font-weight: bold;
      background: linear-gradient(to right, #8D45FF, #3E3CFF, #4374FF);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    /* 取消选中状态下的背景色，仅保留文字高亮 */
    /deep/ .el-menu-item.is-active {
      background-color: transparent !important;
      color: #ffffff !important;
    }

    /deep/ .el-menu-item:hover {
      color: #ffffff !important;
      background-color: transparent !important;
    }
    /deep/ .el-submenu__title:hover,
    /deep/ .el-submenu.is-active .el-submenu__title {
      background-color: transparent !important;
      color: #ffffff !important;
    }

    /deep/ .el-menu.el-menu--horizontal{
      border-bottom:none;
    }
    /deep/ .el-menu--horizontal>.el-menu-item{
      border-bottom:none;
    }
    /deep/ .el-menu--horizontal > .el-menu-item::before,
    /deep/ .el-menu--horizontal > .el-submenu .el-submenu__title::before {
      content: '';
      display: block;
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: #ffffff;
      position: absolute;
      bottom:6px;
      left: 50%;
      transform: translateX(-50%) scale(0);
      transition: transform 0.3s ease;
    }
    /deep/ .el-menu--horizontal > .el-menu-item.is-active::before,
    /deep/ .el-menu--horizontal > .el-submenu.is-active .el-submenu__title::before,
    /deep/ .el-menu--horizontal > .el-menu-item:hover::before,
    /deep/ .el-menu--horizontal > .el-submenu .el-submenu__title:hover::before {
      transform: translateX(-50%) scale(1);
    }
    /deep/.el-menu--horizontal>.el-menu-item.is-active {
      border-bottom:none
    }
    /deep/ .el-menu--horizontal>.el-submenu.is-active .el-submenu__title{
       border-bottom:none
    }
    /deep/ .el-menu--horizontal>.el-submenu .el-submenu__title{
      border-bottom:none
    }
    /deep/ .el-submenu__title i{
      color: #ffffff;
    }
</style>
