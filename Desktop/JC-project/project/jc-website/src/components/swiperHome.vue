<template >
    <!--  -->
  <div class="swiper-container" ref="mySwiper">
    <div class="swiper-wrapper" >
      <div class="swiper-slide" style="position: relative;" v-for="(item, index) in slides" :key="index">
          <img :src="item.baackgroundImage" class="slide-image" />
          <div class="swiper-control" ref="asgcControl">
            <div class="swiper-info">
              <span v-if="item.text1">{{ item.text1 }}</span>
              <span v-if="item.text2">{{ item.text2 }}</span>
              <button class="swiper-button"  v-if="item.buttonText" >{{ item.buttonText }}</button>
            </div>
          </div>
      </div>
    </div>

    <!-- 如果需要分页器 -->


<div class="swiper-pagination"></div>
    <!-- 如果需要导航按钮 -->
    <div class="swiper-button-prev" ></div>
    <div class="swiper-button-next"></div>
  </div>
</template>

<script>
import Swiper from 'swiper'
import 'swiper/css/swiper.css'
import gsap from 'gsap'

export default {
  name: 'MySwiper',
  data() {
    return {
        slides: [
          {
            baackgroundImage: '	https://www.tec-do.com/uploads/images/2023-10-26-16-16-13.jpg',
            text1:'出海',
            text2:'找钛动',
            buttonText:'联系我们',
          },
          //  {
          //   baackgroundImage: 'https://www.tec-do.com/uploads/images/2023-11-01-11-10-06.jpg',
          //   text1:'',
          //   text2:'',
          //   buttonText:'',
          // },
          //  {
          //   baackgroundImage: 'https://www.tec-do.com/uploads/images/2024-12-10-11-35-25.jpg',
          //   text1:'',
          //   text2:'',
          //   buttonText:'',
          // },
          //  {
          //   baackgroundImage: 'https://www.tec-do.com/uploads/images/2025-05-29-17-54-17.jpg',
          //   text1:'',
          //   text2:'',
          //   buttonText:'',
          // },

          //  {
          //   baackgroundImage: 'https://www.tec-do.com/uploads/images/2025-05-26-09-46-29.jpg',
          //   text1:'',
          //   text2:'',
          //   buttonText:'',
          // }
        ]
    }
  },
  mounted() {
    this.mySwiper = new Swiper(this.$refs.mySwiper, {
      loop: true,
      pagination: {
        el: '.swiper-pagination',
        clickable: true
      },
      navigation: {
        nextEl: '.swiper-button-next',
        prevEl: '.swiper-button-prev'
      },
      autoplay: {
        delay: 3000
      },
      slidesPerView: 1,
      spaceBetween: 10,
      breakpoints: {
        640: { slidesPerView: 1 },
        768: { slidesPerView: 1 },
        1024: { slidesPerView: 1 }
      }
    });

    this.$nextTick(() => {
      const controlArray = this.$refs.asgcControl;
      const control = Array.isArray(controlArray) ? controlArray[0] : controlArray;

      if (control) {
        gsap.fromTo(control,
          { opacity: 0, y: 300 },
          { opacity: 1, y: 0, duration: 1.5, ease: 'power3.out' }
        );
      }

      // 选中当前活动 slide 的图片（第一个活动图）
const activeSlide = this.$refs.mySwiper.querySelector('.swiper-slide-active .slide-image');

if (activeSlide) {
  gsap.fromTo(activeSlide,
    { scale: 0.8, opacity: 0, transformOrigin: 'left top' },
    { scale: 1, opacity: 1, duration: 1.5, ease: 'power3.out' }
  );
}
    });
  }
}
</script>

<style scoped>
.swiper-container {
  width: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  border: none;
  height: 100vh;

}

.swiper-control{
  height: 500px;
  z-index: 1;
  position: absolute;
  top: 20%;
  left: 10%;
  text-align: start;
  color: #fff;
  font-size: 100px;
  font-weight: bold;

}

.swiper-info{
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.swiper-button{
  width: 200px;
  height: 50px;
  background-color: #0750fe;
  border: 0;color: #fff;
  border-radius: 5px;
  font-size: 15px;
  margin-top: 50px;
}

.slide-image {
  width: 100%;
  height: auto;
  object-fit: cover;
}
</style>